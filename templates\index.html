<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CIQTEK - NMR预测系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #4361ee;
            --primary-hover: #3a56d4;
            --secondary-color: #3f37c9;
            --accent-color: #4cc9f0;
            --light-bg: #f8f9fa;
            --dark-text: #333;
            --light-text: #f8f9fa;
            --border-radius: 8px;
            --box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --transition: all 0.3s ease;
        }
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 30px 20px;
            line-height: 1.6;
            color: var(--dark-text);
            background-color: #fdfdfd;
        }
        
        .container {
            background-color: white;
            border-radius: var(--border-radius);
            padding: 30px;
            box-shadow: var(--box-shadow);
        }
        
        h1 {
            color: var(--primary-color);
            margin-bottom: 10px;
            font-weight: 600;
            font-size: 28px;
        }
        
        p.description {
            color: #666;
            margin-bottom: 25px;
            font-size: 16px;
        }
        
        .form-group {
            margin-bottom: 25px;
            background-color: var(--light-bg);
            padding: 20px;
            border-radius: var(--border-radius);
            border-left: 4px solid var(--primary-color);
        }
        
        label {
            display: block;
            margin-bottom: 10px;
            font-weight: 600;
            color: var(--secondary-color);
            font-size: 16px;
        }
        
        textarea {
            width: 100%;
            height: 120px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: var(--border-radius);
            font-family: monospace;
            font-size: 15px;
            transition: var(--transition);
            resize: vertical;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        textarea:focus {
            outline: none;
            border-color: var(--accent-color);
            box-shadow: 0 0 0 3px rgba(76, 201, 240, 0.3);
        }
        
        button {
            background-color: var(--primary-color);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: var(--border-radius);
            /* cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            transition: var(--transition); */
            box-shadow: 0 4px 6px rgba(67, 97, 238, 0.3);

            /* background: linear-gradient(135deg, #4cc9f0, #4361ee); */
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            /* box-shadow: 0 5px 15px rgba(67, 97, 238, 0.4); */
            display: flex
        ;
            align-items: center;
            gap: 10px;
            margin: 20px auto;
        }
        
        button:hover {
            background-color: var(--primary-hover);
            transform: translateY(-2px);
            box-shadow: 0 6px 8px rgba(67, 97, 238, 0.4);
        }
        
        button:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(67, 97, 238, 0.3);
        }
        
        /* Add styles for disabled button */
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
            opacity: 0.7;
        }
        
        .loading {
            display: none;
            margin: 30px 0;
            text-align: center;
        }
        
        .spinner {
            width: 70px;
            text-align: center;
            margin: 0 auto;
        }
        
        .spinner > div {
            width: 18px;
            height: 18px;
            background-color: var(--primary-color);
            border-radius: 100%;
            display: inline-block;
            animation: sk-bouncedelay 1.4s infinite ease-in-out both;
        }
        
        .spinner .bounce1 {
            animation-delay: -0.32s;
        }
        
        .spinner .bounce2 {
            animation-delay: -0.16s;
        }
        
        @keyframes sk-bouncedelay {
            0%, 80%, 100% { 
                transform: scale(0);
            } 40% { 
                transform: scale(1.0);
            }
        }
        
        .loading-text {
            margin-top: 15px;
            font-size: 16px;
            color: var(--primary-color);
            font-weight: 500;
        }
        
        .progress-bar {
            height: 4px;
            width: 100%;
            background-color: #e9ecef;
            border-radius: 2px;
            margin-top: 15px;
            overflow: hidden;
        }
        
        .progress-bar-fill {
            height: 100%;
            background-color: var(--primary-color);
            border-radius: 2px;
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .results {
            margin-top: 30px;
            display: none;
            animation: fadeIn 0.5s ease;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #eee;
        }
        
        .results-header h2 {
            color: var(--secondary-color);
            font-weight: 600;
            margin: 0;
        }
        
        .results-meta {
            display: flex;
            gap: 20px;
        }
        
        .result-meta-item {
            display: flex;
            flex-direction: column;
        }
        
        .meta-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 2px;
        }
        
        .meta-value {
            font-size: 14px;
            font-weight: 600;
            color: var(--dark-text);
        }
        
        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            margin-top: 10px;
            overflow: hidden;
            border-radius: var(--border-radius);
            box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
        }
        
        th, td {
            padding: 12px 15px;
            text-align: left;
        }
        
        th {
            background-color: var(--primary-color);
            color: white;
            font-weight: 500;
            text-transform: uppercase;
            font-size: 14px;
            letter-spacing: 1px;
        }
        
        th:first-child {
            border-top-left-radius: var(--border-radius);
        }
        
        th:last-child {
            border-top-right-radius: var(--border-radius);
        }
        
        tr:nth-child(even) {
            background-color: #f2f8ff;
        }
        
        tr:hover {
            background-color: #e6f3ff;
        }
        
        .error {
            color: #e63946;
            margin-top: 15px;
            padding: 15px;
            border-radius: var(--border-radius);
            background-color: #fde8e8;
            border-left: 4px solid #e63946;
            font-weight: 500;
            display: none;
        }
        
        .info {
            margin-top: 10px;
            font-style: italic;
            color: #666;
            font-size: 14px;
        }
        
        .structure-img {
            width: 180px;
            height: 150px;
            background-color: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border-radius: 4px;
            margin: 10px 0;
            object-fit: contain;
        }
        
        .formula {
            font-family: "Times New Roman", Times, serif;
            font-size: 16px;
            color: var(--secondary-color);
            font-weight: 600;
            padding: 8px 0;
        }
        
        .name {
            font-weight: 600;
            color: var(--dark-text);
            margin-bottom: 5px;
        }
        
        .mol-weight {
            font-size: 14px;
            color: #666;
        }
        
        .structure-container {
            display: flex;
            flex-direction: column;
            min-width: 200px;
        }
        
        .example-section {
            margin-top: 15px;
        }
        
        .examples-header {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
        }
        
        .examples-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .example-input {
            background-color: #eef2ff;
            padding: 8px 12px;
            border-radius: 16px;
            color: var(--primary-color);
            cursor: pointer;
            font-size: 14px;
            transition: var(--transition);
            border: 1px solid #d8e3fd;
        }
        
        .example-input:hover {
            background-color: #d8e3fd;
            transform: translateY(-2px);
        }
        
        .btn-container {
            display: flex;
            justify-content: flex-start;
        }
        
        /* 添加响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            
            .results-header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .results-meta {
                margin-top: 15px;
                flex-wrap: wrap;
            }
            
            .structure-img {
                width: 150px;
                height: 120px;
            }
        }

        /* 水印样式 */
        .watermark {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 9999;
            display: flex;
            flex-wrap: wrap;
            overflow: hidden;
            opacity: 0.1;
            user-select: none;
        }

        .watermark-item {
            font-size: 48px;
            color: #000;
            transform: rotate(-30deg);
            white-space: nowrap;
            padding: 100px;
            font-family: Arial, sans-serif;
            font-weight: bold;
        }

        /* 版权栏样式 */
        .footer {
            margin-top: 50px;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 14px;
            line-height: 1.6;
        }

        .footer p {
            margin: 5px 0;
        }

        .footer .company-name {
            font-weight: 600;
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <!-- 添加水印容器 -->
    <div class="watermark" id="watermark"></div>
    <div class="container">
        <!-- <h1>CIQTEK - NMR AI预测系统</h1> -->
        <h1><i class="fas fa-atom"></i> AI NMR分子结构预测</h1>
        <p class="description">输入NMR C谱的ppm列表，系统将预测最可能的化学结构，以帮助您的研究分析</p>
        
        <div class="form-group">
            <label for="nmr-input">PPM (每个值用逗号分隔):</label>
            <textarea id="nmr-input" placeholder="例如: 171.03 70.66 27.83 20.85 19.12	"></textarea>
            <div class="info">可接受数字、逗号、空格和换行符</div>
            
            <div class="example-section">
                <div class="examples-header">快速示例:</div>
                <div class="examples-container">
                    <div class="example-input" onclick="useExample('171.03 70.66 27.83 20.85 19.12	')">示例1: isobutyl acetate</div>
                    <div class="example-input" onclick="useExample('166.50 132.70 130.62 129.53 128.27 69.87 35.79 32.53 20.05 17.03 14.30')">示例2: 2-Methylpentyl benzoate</div>
                    <div class="example-input" onclick="useExample('137.50, 115.03, 63.50')">示例3: Allyl alcohol</div>
                    <div class="example-input" onclick="useExample('57.79,18.13')">示例4: ethyl alcohol</div>
                </div>
            </div>
        </div>
        
        <div class="btn-container">
            <button id="predict-btn"><i class="fas fa-brain ai-icon"></i> 预测分子结构</button>
            <button id="deep-thinking" disabled><i class="fas fa-brain ai-atom"></i> 深度思考 (即将开放)</button>
        </div>
        
        <div id="loading" class="loading">
            <div class="spinner">
                <div class="bounce1"></div>
                <div class="bounce2"></div>
                <div class="bounce3"></div>
            </div>
            <p class="loading-text">正在分析NMR数据，请稍候...</p>
            <div class="progress-bar">
                <div class="progress-bar-fill" id="progress-fill"></div>
            </div>
        </div>
        
        <div id="error" class="error"></div>
        
        <div id="results" class="results">
            <div class="results-header">
                <h2>预测结果</h2>
                <div class="results-meta">
                    <div class="prediction-info">提示：以下结果由AI自动预测完成，内容仅供参考，请仔细甄别。</div>
                    <!-- <div class="result-meta-item">
                        <span class="meta-label">输入的NMR值</span>
                        <span class="meta-value" id="input-nmr"></span>
                    </div>
                    <div class="result-meta-item">
                        <span class="meta-label">搜索用时</span>
                        <span class="meta-value"><span id="search-time"></span> 秒</span>
                    </div> -->
                </div>
            </div>
            
            <table>
                <thead>
                    <tr>
                        <th>排名</th>
                        <th>匹配度</th>
                        <th>分子式</th>
                        <th>分子量</th>  <!-- 新增分子量列 -->
                        <th>化学结构</th>
                        <th>查看</th>
                    </tr>
                </thead>
                <tbody id="results-table">
                    <!-- 结果会动态添加到这里 -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- 添加版权栏 -->
    <footer class="footer">
        <p><span class="company-name">CIQTEK</span> - 专业的科学仪器及解决方案提供商</p>
        <p>© <span id="current-year"></span> CIQTEK. All Rights Reserved. 保留所有权利。</p>
    </footer>

    <script>
        // 设置当前年份
        document.getElementById('current-year').textContent = new Date().getFullYear();

        // 创建水印
        function createWatermark() {
            const watermarkContainer = document.getElementById('watermark');
            const windowWidth = window.innerWidth;
            const windowHeight = window.innerHeight;
            const text = 'CIQTEK';
            
            // 清空现有水印
            watermarkContainer.innerHTML = '';
            
            // 计算需要多少个水印元素来填充屏幕
            const itemWidth = 300; // 每个水印元素的大致宽度
            const itemHeight = 200; // 每个水印元素的大致高度
            const cols = Math.ceil(windowWidth / itemWidth) + 1;
            const rows = Math.ceil(windowHeight / itemHeight) + 1;
            
            // 创建水印网格
            for (let i = 0; i < rows; i++) {
                for (let j = 0; j < cols; j++) {
                    const watermarkItem = document.createElement('div');
                    watermarkItem.className = 'watermark-item';
                    watermarkItem.textContent = text;
                    watermarkContainer.appendChild(watermarkItem);
                }
            }
        }

        // 初始创建水印
        createWatermark();
        
        // 窗口大小改变时重新创建水印
        window.addEventListener('resize', createWatermark);

        // 示例输入函数
        function useExample(example) {
            document.getElementById('nmr-input').value = example;
        }
        
        // 进度条模拟函数
        function simulateProgress() {
            const progressFill = document.getElementById('progress-fill');
            let width = 0;
            const duration = 10000; // 5秒
            const interval = 50; // 更新间隔(ms)
            const increment = 100 / (duration / interval);
            
            return new Promise((resolve) => {
                const timer = setInterval(() => {
                    if (width >= 95) {
                        clearInterval(timer);
                        setTimeout(() => {
                            progressFill.style.width = '100%';
                            setTimeout(resolve, 200);
                        }, 300);
                    } else {
                        width += increment;
                        // 给进度条一些随机性，使其看起来更自然
                        const randomFactor = 0.8 + Math.random() * 0.4;
                        const actualIncrement = increment * randomFactor;
                        width = Math.min(width + actualIncrement, 95);
                        progressFill.style.width = width + '%';
                    }
                }, interval);
            });
        }
        
        document.getElementById('predict-btn').addEventListener('click', async function() {
            // 获取输入值
            const inputText = document.getElementById('nmr-input').value.trim();
            
            if (!inputText) {
                showError('请输入NMR值');
                return;
            }
            
            // 解析输入，支持多种分隔符
            const cleanedInput = inputText.replace(/\s+/g, ',').replace(/,+/g, ',').replace(/^,|,$/g, '');
            const nmrValues = cleanedInput.split(',').filter(val => val.trim() !== '');
            
            if (nmrValues.length === 0) {
                showError('无法解析输入值');
                return;
            }
            
            // 显示加载状态
            document.getElementById('loading').style.display = 'block';
            document.getElementById('error').style.display = 'none';
            document.getElementById('results').style.display = 'none';
            
            // 模拟进度条 - 等待约5秒
            const progressFill = document.getElementById('progress-fill');
            progressFill.style.width = '0%';
            await simulateProgress();
            
            try {
                // 发送预测请求
                const response = await fetch('/predict', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ nmr_values: nmrValues })
                });
                
                const data = await response.json();
                
                // 隐藏加载状态
                document.getElementById('loading').style.display = 'none';
                
                if (data.error) {
                    showError(data.error);
                    return;
                }
                
                // 显示结果
                // document.getElementById('input-nmr').textContent = data.input_nmr.join(', ');
                // document.getElementById('search-time').textContent = data.search_time.toFixed(2);
                
                const resultsTable = document.getElementById('results-table');
                resultsTable.innerHTML = '';
                
                // 渲染每个结果
                data.results.forEach(result => {
                    const row = document.createElement('tr');
                    
                    // 创建单元格
                    const rankCell = document.createElement('td');
                    rankCell.textContent = result.rank;
                    
                    const scoreCell = document.createElement('td');
                    scoreCell.textContent = result.score.toFixed(4);
                    
                    // 分子式单元格
                    const formulaCell = document.createElement('td');
                    const formulaDiv = document.createElement('div');
                    formulaDiv.className = 'formula';
                    formulaDiv.innerHTML = result.formula;
                    formulaCell.appendChild(formulaDiv);
                    
                    // 新增分子量单元格
                    const weightCell = document.createElement('td');
                    weightCell.className = 'mol-weight';
                    weightCell.textContent = result.mol_weight.toFixed(2);


                    // 化学结构单元格
                    const structureCell = document.createElement('td');
                    const structureContainer = document.createElement('div');
                    structureContainer.className = 'structure-container';

                    // 查看详情按钮
                    const viewCell = document.createElement('td');
                    const viewButton = document.createElement('button');
                    viewButton.textContent = '查看详情';
                    viewButton.className = 'view-btn';
                    viewButton.style.cssText = `
                        background-color: var(--accent-color);
                        color: white;
                        border: none;
                        padding: 8px 16px;
                        border-radius: 4px;
                        cursor: pointer;
                        font-size: 14px;
                        transition: var(--transition);
                        margin: 0;
                        display: inline-block;
                    `;

                    // 绑定分子式到按钮的点击事件
                    viewButton.addEventListener('click', function() {
                        alert('分子式: ' + result.formula + '\n分子量: ' + result.mol_weight.toFixed(2));
                    });

                    // 添加悬停效果
                    viewButton.addEventListener('mouseenter', function() {
                        this.style.backgroundColor = '#3a9bc1';
                        this.style.transform = 'translateY(-1px)';
                    });

                    viewButton.addEventListener('mouseleave', function() {
                        this.style.backgroundColor = 'var(--accent-color)';
                        this.style.transform = 'translateY(0)';
                    });

                    viewCell.appendChild(viewButton);
                    
                    // 创建图片元素
                    const img = document.createElement('img');
                    img.className = 'structure-img';
                    img.src = 'data:image/png;base64,' + result.img_base64;
                    img.alt = result.name;
                    
                    // 构建DOM结构
                    structureContainer.appendChild(img);
                    structureCell.appendChild(structureContainer);
                    
                    // 添加单元格到行
                    row.appendChild(rankCell);
                    row.appendChild(scoreCell);
                    row.appendChild(formulaCell);
                    row.appendChild(weightCell);  // 新增分子量列
                    row.appendChild(structureCell);
                    row.appendChild(viewCell);
                    
                    // 添加行到表格
                    resultsTable.appendChild(row);
                });
                
                document.getElementById('results').style.display = 'block';
                
            } catch (error) {
                document.getElementById('loading').style.display = 'none';
                showError('发生错误: ' + error.message);
            }
        });
        
        function showError(message) {
            const errorDiv = document.getElementById('error');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }
    </script>
</body>
</html>